import { request } from '@umijs/max';

/** 查询列表  GET /orders */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Order[] }>>(
    '/orders',
    {
      method: 'GET',
      params,
    },
  );
}

/** 导出订单Excel  GET /orders/export */
export async function exportOrders(params: Record<string, any>) {
  return request('/orders/export', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

/** 获取订单日志 /orders/:orderId/logs */
export async function logs(id: number) {
  return request<API.ResType<API.ServiceChangeLog[]>>(`/orders/${id}/logs`, {
    method: 'GET',
  });
}

/** 按ID查询  GET /orders/:id */
export async function show(id: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}`, {
    method: 'GET',
  });
}

/** 派单 Post /orders/{orderId}/deliver */
export async function deliver(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/deliver`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 转单 Post /orders/{orderId}/transfer */
export async function transfer(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/transfer`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 开始服务 Post /orders/{orderId}/start */
export async function start(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/start`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 完成订单 Post /orders/{orderId}/complete */
export async function complete(id: number, employeeId: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/complete`, {
    method: 'POST',
    data: {
      employeeId,
    },
  });
}

/** 审核退款 Post /orders/:sn/auditRefund */
export async function auditRefund(
  sn: string,
  info: { result: boolean; reason?: string; money?: number },
) {
  return request<API.ResType<API.Order>>(`/orders/${sn}/auditRefund`, {
    method: 'POST',
    data: info,
  });
}

/** 退款 Post /wepay/refund/:sn */
export async function refund(sn: string) {
  return request<API.ResType<API.Order>>(`/wepay/refund/${sn}`, {
    method: 'POST',
  });
}

/** 修改服务地址 PUT /orders/:orderId/updateServiceAddress */
export async function updateServiceAddress(
  orderId: number,
  data: {
    address?: string;
    addressDetail?: string;
    longitude?: number;
    latitude?: number;
    addressRemark?: string;
    addressId?: number | null;
    employeeId?: number;
    userType?: 'admin' | 'employee' | 'customer';
  },
) {
  return request<API.ResType<boolean>>(
    `/orders/${orderId}/updateServiceAddress`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
    },
  );
}

/** 取消订单 Podt /orders/{orderId}/cancel */
export async function cancel(id: number) {
  return request<API.ResType<API.Order>>(`/orders/${id}/cancel`, {
    method: 'POST',
  });
}

/** 删除  DELETE /orders/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/orders/${id}`, {
    method: 'DELETE',
  });
}

// 管理员专用接口

/** 管理员取消订单 POST /admin/orders/{orderId}/admin-cancel */
export async function adminCancel(
  orderId: number,
  data: { operatorId: number; reason?: string },
) {
  return request<API.ResType<boolean>>(
    `/admin/orders/${orderId}/admin-cancel`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
    },
  );
}

/** 管理员申请退款 POST /admin/orders/sn/{sn}/admin-apply-refund */
export async function adminApplyRefund(
  sn: string,
  data: { operatorId: number; reason?: string },
) {
  return request<API.ResType<boolean>>(
    `/admin/orders/sn/${sn}/admin-apply-refund`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data,
    },
  );
}

/** 管理员删除订单 DELETE /admin/orders/{orderId}/admin-delete */
export async function adminDelete(
  orderId: number,
  data: { operatorId: number; reason?: string },
) {
  return request<API.ResType<any>>(`/admin/orders/${orderId}/admin-delete`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 订单统计相关接口

/** 订单概览统计  GET /order-statistics/overview */
export async function overview(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<{
      orderStats: {
        total: number;
        today: number;
        month: number;
      };
      statusStats: Array<{
        status: string;
        count: number;
      }>;
      revenueStats: {
        total: number;
        today: number;
        month: number;
        completedOrders: number;
      };
    }>
  >('/order-statistics/overview', {
    method: 'GET',
    params,
  });
}

/** 订单趋势统计  GET /order-statistics/trend */
export async function trend(params: {
  startDate: string;
  endDate: string;
  groupBy?: 'day' | 'week' | 'month';
}) {
  return request<
    API.ResType<
      Array<{
        period: string;
        orderCount: number;
        totalAmount: number;
        avgAmount: number;
      }>
    >
  >('/order-statistics/trend', {
    method: 'GET',
    params,
  });
}

/** 订单状态分布统计  GET /order-statistics/status-distribution */
export async function statusDistribution(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<
      Array<{
        status: string;
        count: number;
        percentage: string;
        totalAmount: number;
      }>
    >
  >('/order-statistics/status-distribution', {
    method: 'GET',
    params,
  });
}

/** 员工订单统计  GET /order-statistics/employee */
export async function employeeStats(params?: {
  startDate?: string;
  endDate?: string;
  employeeId?: number;
  sortBy?: 'orderCount' | 'totalAmount' | 'rating';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}) {
  return request<
    API.ResType<{
      list: Array<{
        employeeId: number;
        employeeName: string;
        employeePhone: string;
        employeeAvatar?: string;
        employeeRating: number;
        orderCount: number;
        totalAmount: number;
        avgAmount: number;
      }>;
      total: number;
      page: number;
      pageSize: number;
    }>
  >('/order-statistics/employee', {
    method: 'GET',
    params,
  });
}

/** 客户订单统计  GET /order-statistics/customer */
export async function customerStats(params?: {
  startDate?: string;
  endDate?: string;
  customerId?: number;
  sortBy?: 'orderCount' | 'totalAmount' | 'avgAmount';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  pageSize?: number;
}) {
  return request<
    API.ResType<{
      list: Array<{
        customerId: number;
        customerName: string;
        customerPhone: string;
        customerAvatar?: string;
        memberStatus: number;
        orderCount: number;
        totalAmount: number;
        avgAmount: number;
      }>;
      total: number;
      page: number;
      pageSize: number;
    }>
  >('/order-statistics/customer', {
    method: 'GET',
    params,
  });
}

/** 服务类型统计  GET /order-statistics/service-type */
export async function serviceTypeStats(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<
      Array<{
        serviceTypeId: number;
        serviceTypeName: string;
        serviceTypeCode: string;
        orderCount: number;
        totalAmount: number;
      }>
    >
  >('/order-statistics/service-type', {
    method: 'GET',
    params,
  });
}

/** 地区订单统计  GET /order-statistics/region */
export async function regionStats(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<
      Array<{
        region: string;
        orderCount: number;
        totalAmount: number;
        avgAmount: number;
      }>
    >
  >('/order-statistics/region', {
    method: 'GET',
    params,
  });
}

/** 时段订单统计  GET /order-statistics/time-period */
export async function timePeriodStats(params?: {
  startDate?: string;
  endDate?: string;
  periodType?: 'hour' | 'weekday';
}) {
  return request<
    API.ResType<
      Array<{
        period: number;
        periodLabel: string;
        orderCount: number;
        totalAmount: number;
        avgAmount: number;
      }>
    >
  >('/order-statistics/time-period', {
    method: 'GET',
    params,
  });
}

/** 订单金额分布统计  GET /order-statistics/amount-distribution */
export async function amountDistribution(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<
      Array<{
        range: string;
        min: number;
        max: number;
        count: number;
        percentage: string;
      }>
    >
  >('/order-statistics/amount-distribution', {
    method: 'GET',
    params,
  });
}

/** 获取用户有效订单原价统计  GET /order-statistics/customer-original-price */
export async function customerOriginalPriceStats(params: {
  customerId: number;
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<{
      customerId: number;
      orderCount: number;
      mainOrderOriginalPrice: number;
      mainOrderPaidAmount: number;
      additionalServiceOriginalPrice: number;
      additionalServicePaidAmount: number;
      totalOriginalPrice: number;
      totalPaidAmount: number;
      totalCardDeduction: number;
      totalCouponDeduction: number;
      totalDeduction: number;
    }>
  >('/order-statistics/customer-original-price', {
    method: 'GET',
    params,
  });
}

/** 获取用户有效订单列表  GET /order-statistics/customer-valid-orders */
export async function customerValidOrders(params: {
  customerId: number;
  startDate?: string;
  endDate?: string;
  sortBy?: 'orderTime' | 'serviceTime' | 'originalPrice' | 'totalFee';
  sortOrder?: 'asc' | 'desc';
  current?: number;
  pageSize?: number;
}) {
  return request<
    API.ResType<{
      list: Array<{
        id: number;
        sn: string;
        status: string;
        orderTime: string;
        serviceTime: string;
        address: string;
        addressDetail: string;
        originalPrice: number;
        totalFee: number;
        cardDeduction: number;
        couponDeduction: number;
        additionalServiceOriginalPrice: number;
        additionalServiceAmount: number;
        totalOriginalPrice: number;
        totalPaidAmount: number;
        mainServices: Array<{
          id: number;
          serviceId: number;
          serviceName: string;
          servicePrice: number;
          serviceType: string;
          serviceTypeName: string;
          petName: string;
          petType: string;
          petBreed: string;
          userRemark: string;
          additionalServices: Array<{
            id: number;
            name: string;
            type: string;
            price: number;
          }>;
        }>;
        additionalServiceOrders: Array<{
          id: number;
          sn: string;
          status: string;
          originalPrice: number;
          totalFee: number;
          cardDeduction: number;
          couponDeduction: number;
          createdAt: string;
          services: Array<{
            id: number;
            serviceId: number;
            serviceName: string;
            servicePrice: number;
            quantity: number;
            serviceType: string;
            serviceTypeName: string;
          }>;
        }>;
      }>;
      total: number;
      page: number;
      pageSize: number;
    }>
  >('/order-statistics/customer-valid-orders', {
    method: 'GET',
    params,
  });
}
