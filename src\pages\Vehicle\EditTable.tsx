import { DictionarieState } from '@/models/dictionarie';
import { create, index, remove, update } from '@/services/vehicles';
import { getVehicleTypeNameFromEmpolyee } from '@/utils/calc';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Button, message, Popconfirm, Space, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import DetailModal from './DetailModal';
import EditModal from './EditModal';

const { Text } = Typography;

type EditTableProps = {
  /** 设置标记点 */
  setMarkers: React.Dispatch<React.SetStateAction<Marker[]>>;
  /** 设置激活的标记点 */
  setActiveMarkers: (id: number) => void;
  /** 字典数据 */
  dictionarie: DictionarieState;
};

const EditTable: React.FC<EditTableProps> = ({
  setMarkers,
  setActiveMarkers,
  dictionarie,
}) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [current, setCurrent] = useState<API.Vehicle | undefined>(undefined);

  /** 格式化日期 */
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '-';
    return dayjs(dateStr).format('YYYY-MM-DD');
  };

  /** 检查是否即将到期 */
  const isExpiringSoon = (dateStr?: string, days = 30) => {
    if (!dateStr) return false;
    const expiry = dayjs(dateStr);
    const now = dayjs();
    return expiry.diff(now, 'day') <= days && expiry.isAfter(now);
  };

  /** 检查是否已过期 */
  const isExpired = (dateStr?: string) => {
    if (!dateStr) return false;
    return dayjs(dateStr).isBefore(dayjs());
  };

  /** 渲染到期状态 */
  const renderExpiryStatus = (dateStr?: string) => {
    if (!dateStr) return '-';

    const date = formatDate(dateStr);
    if (isExpired(dateStr)) {
      return <Text type="danger">{date}</Text>;
    } else if (isExpiringSoon(dateStr)) {
      return <Text type="warning">{date}</Text>;
    } else {
      return <Text type="success">{date}</Text>;
    }
  };

  const handleSave = async (values: API.Vehicle) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.Vehicle) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Vehicle, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '车牌号',
      dataIndex: 'plateNumber',
      key: 'plateNumber',
      width: 120,
      fixed: 'left',
    },
    {
      title: '车辆类型',
      dataIndex: 'vehicleType',
      key: 'vehicleType',
      width: 100,
      render: (_, record) =>
        getVehicleTypeNameFromEmpolyee(
          dictionarie?.list || [],
          record.employee?.position,
        ) || '-',
    },
    {
      title: '车辆状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => (
        <Tag color={record.status === '空闲' ? 'green' : 'blue'}>
          {record.status}
        </Tag>
      ),
    },
    {
      title: '里程数',
      dataIndex: 'mileage',
      key: 'mileage',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (record.mileage ? `${record.mileage} 公里` : '-'),
    },
    {
      title: '保险到期',
      dataIndex: 'insuranceExpiry',
      key: 'insuranceExpiry',
      width: 120,
      hideInSearch: true,
      render: (_, record) => renderExpiryStatus(record.insuranceExpiry),
    },
    {
      title: '行驶证到期',
      dataIndex: 'licenseExpiry',
      key: 'licenseExpiry',
      width: 120,
      hideInSearch: true,
      render: (_, record) => renderExpiryStatus(record.licenseExpiry),
    },
    {
      title: '员工',
      dataIndex: ['employee', 'name'],
      key: 'employee',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '手机号',
      dataIndex: ['employee', 'phone'],
      key: 'phone',
      width: 120,
      hideInSearch: true,
    },
    {
      title: '最后提交时间',
      dataIndex: 'lastSubmittedAt',
      key: 'lastSubmittedAt',
      width: 140,
      hideInSearch: true,
      render: (_, record) =>
        record.lastSubmittedAt
          ? dayjs(record.lastSubmittedAt).format('MM-DD HH:mm')
          : '-',
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setDetailVisible(true);
            }}
          >
            详情
          </Button>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Vehicle>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        scroll={{ x: 1200 }}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          setMarkers(
            (data?.list || [])
              .filter((item) => !!item.longitude && !!item.latitude)
              .map((item) => ({
                position: [item.longitude!, item.latitude!],
                title: item.plateNumber,
                id: item.id,
                icon: {
                  image: '/logo.png',
                  size: [36, 36],
                  imageSize: [36, 36],
                  // offset?: [number, number]; // 图标偏移量 [x, y]
                },
              })),
          );
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
        onRow={(record) => ({
          onClick: () => {
            setActiveMarkers(record.id);
          },
        })}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
      <DetailModal
        open={detailVisible}
        vehicle={current}
        onClose={() => setDetailVisible(false)}
      />
    </>
  );
};

export default connect(({ dictionarie }: { dictionarie: any }) => ({
  dictionarie,
}))(EditTable);
