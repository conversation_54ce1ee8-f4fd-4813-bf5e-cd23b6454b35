/*
 * @Description: 订单详细列表
 * @Date: 2025-07-16 10:19:20
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 10:41:03
 */
import { DictionarieState } from '@/models/dictionarie';
import { connect } from '@umijs/max';
import { Col, Collapse, CollapseProps, Row, Segmented } from 'antd';
import React from 'react';

const DetailList: React.FC<{
  dictionarie: DictionarieState;
}> = ({ dictionarie }) => {
  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: '查询条件',
      children: (
        <>
          <Row>
            <Col>服务类型：</Col>
            <Col>
              <Segmented<string>
                options={['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly']}
                onChange={(value) => {
                  console.log(value); // string
                }}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];
  return (
    <div>
      {/* 查询条件 */}
      <Collapse items={items} defaultActiveKey={['1']} />
      {/* 列表 */}
    </div>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(DetailList);
