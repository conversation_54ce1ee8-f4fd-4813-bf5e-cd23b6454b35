/*
 * @Description: 订单详细列表
 * @Date: 2025-07-16 10:19:20
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 17:13:49
 */
import { DictionarieState } from '@/models/dictionarie';
import { index, exportOrders } from '@/services/order';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import {
  Button,
  Col,
  Collapse,
  CollapseProps,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Space,
} from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import React, { useRef, useState } from 'react';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface DetailListProps {
  dictionarie: DictionarieState;
  columns: ProColumns<API.Order, 'text'>[];
  checkOrderComplaints: (orderId: number) => Promise<any>;
  getUnhandledComplaintsCount: (orderId: number) => number;
  handleViewComplaints: (record: API.Order) => void;
  setCurrent: (order: API.Order | undefined) => void;
  setModalVisible: (visible: boolean) => void;
}

const DetailList: React.FC<DetailListProps> = ({
  dictionarie,
  columns,
  checkOrderComplaints,
}) => {
  const [form] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const [searchParams, setSearchParams] = React.useState<any>({});
  const [exporting, setExporting] = useState(false);

  // 处理搜索
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const params: any = {};

    // 处理时间范围
    if (values.timeRange && values.timeRange.length === 2) {
      params.startTime = values.timeRange[0].startOf('day').toISOString();
      params.endTime = values.timeRange[1].endOf('day').toISOString();
    }

    // 处理其他字段
    if (values.phone) params.phone = values.phone;
    if (values.customerName) params.customerName = values.customerName;
    if (values.employeename) params.employeename = values.employeename;
    if (values.serviceType) params.serviceType = values.serviceType;
    if (values.status) params.status = values.status;

    setSearchParams(params);
    actionRef.current?.reload();
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    setSearchParams({});
    actionRef.current?.reload();
  };

  // 处理导出
  const handleExport = async () => {
    try {
      setExporting(true);

      // 获取当前搜索条件
      const values = form.getFieldsValue();
      const exportParams: any = {};

      // 处理时间范围
      if (values.timeRange && values.timeRange.length === 2) {
        exportParams.startTime = values.timeRange[0].startOf('day').toISOString();
        exportParams.endTime = values.timeRange[1].endOf('day').toISOString();
      }

      // 处理其他字段
      if (values.phone) exportParams.phone = values.phone;
      if (values.customerName) exportParams.customerName = values.customerName;
      if (values.employeename) exportParams.employeename = values.employeename;
      if (values.serviceType) exportParams.serviceType = values.serviceType;
      if (values.status) exportParams.status = values.status;

      // 调用导出接口
      const response = await exportOrders(exportParams);

      // 创建下载链接
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 生成文件名
      const now = dayjs();
      const fileName = `orders_${now.format('YYYYMMDD_HHmmss')}.xlsx`;
      link.download = fileName;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    } finally {
      setExporting(false);
    }
  };

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: '查询条件',
      children: (
        <Form form={form} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Form.Item label="客户手机号" name="phone">
                <Input placeholder="请输入客户手机号" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="客户昵称" name="customerName">
                <Input placeholder="请输入客户昵称" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="服务人员" name="employeename">
                <Input placeholder="请输入员工姓名" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="服务类型" name="serviceType">
                <Select placeholder="请选择服务类型" allowClear>
                  {(dictionarie?.list || [])
                    .filter(
                      (item) => item.status === 1 && item.type === '服务类型',
                    )
                    .map((item) => (
                      <Option key={item.code} value={item.code}>
                        {item.name}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="创建时间" name="timeRange">
                <RangePicker
                  style={{ width: '100%' }}
                  placeholder={['开始时间', '结束时间']}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="订单状态" name="status">
                <Select placeholder="请选择订单状态" allowClear>
                  <Option value="待付款">待付款</Option>
                  <Option value="待接单">待接单</Option>
                  <Option value="待服务">待服务</Option>
                  <Option value="已出发">已出发</Option>
                  <Option value="服务中">服务中</Option>
                  <Option value="已完成">已完成</Option>
                  <Option value="已评价">已评价</Option>
                  <Option value="已取消">已取消</Option>
                  <Option value="已退款">已退款</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={10}>
              <Form.Item label=" " colon={false}>
                <Space>
                  <Button type="primary" onClick={handleSearch}>
                    查询
                  </Button>
                  <Button onClick={handleReset}>重置</Button>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={handleExport}
                    loading={exporting}
                  >
                    导出Excel
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      ),
    },
  ];

  return (
    <div>
      {/* 查询条件 */}
      <Collapse items={items} defaultActiveKey={['1']} />
      {/* 列表 */}
      <ProTable<API.Order>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params, _sort, filter) => {
          const { errCode, msg, data } = await index({
            ...params,
            ...searchParams,
            filter,
            includeAdditionalServices: 'true',
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }

          // 预加载投诉数据
          const orders = data?.list || [];
          const complaintPromises = orders.map((order) =>
            checkOrderComplaints(order.id).catch(() => []),
          );
          await Promise.all(complaintPromises);

          return {
            data: orders,
            total: data?.total || 0,
          };
        }}
        scroll={{ x: '100%' }}
        search={false}
        toolBarRender={() => []}
      />
    </div>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(DetailList);
