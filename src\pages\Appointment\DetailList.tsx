/*
 * @Description: 订单详细列表
 * @Date: 2025-07-16 10:19:20
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-07-16 10:45:47
 */
import { DictionarieState } from '@/models/dictionarie';
import { connect } from '@umijs/max';
import {
  Col,
  Collapse,
  CollapseProps,
  Row,
  Input,
  DatePicker,
  Select,
  Button,
  Space,
  Form
} from 'antd';
import React from 'react';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface DetailListProps {
  dictionarie: DictionarieState;
  onSearch?: (values: any) => void;
  onReset?: () => void;
}

const DetailList: React.FC<DetailListProps> = ({
  dictionarie,
  onSearch,
  onReset
}) => {
  const [form] = Form.useForm();

  // 处理搜索
  const handleSearch = () => {
    const values = form.getFieldsValue();
    const searchParams: any = {};

    // 处理时间范围
    if (values.timeRange && values.timeRange.length === 2) {
      searchParams.startTime = values.timeRange[0].startOf('day').toISOString();
      searchParams.endTime = values.timeRange[1].endOf('day').toISOString();
    }

    // 处理其他字段
    if (values.phone) searchParams.phone = values.phone;
    if (values.customerName) searchParams.customerName = values.customerName;
    if (values.employeename) searchParams.employeename = values.employeename;
    if (values.serviceType) searchParams.serviceType = values.serviceType;
    if (values.status) searchParams.status = values.status;

    onSearch?.(searchParams);
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  const items: CollapseProps['items'] = [
    {
      key: '1',
      label: '查询条件',
      children: (
        <Form form={form} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Form.Item label="客户手机号" name="phone">
                <Input placeholder="请输入客户手机号" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="客户昵称" name="customerName">
                <Input placeholder="请输入客户昵称" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="服务人员" name="employeename">
                <Input placeholder="请输入员工姓名" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="服务类型" name="serviceType">
                <Select placeholder="请选择服务类型" allowClear>
                  {(dictionarie?.list || [])
                    .filter((item) => item.status === 1 && item.type === '服务类型')
                    .map((item) => (
                      <Option key={item.code} value={item.code}>
                        {item.name}
                      </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="创建时间" name="timeRange">
                <RangePicker
                  style={{ width: '100%' }}
                  placeholder={['开始时间', '结束时间']}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="订单状态" name="status">
                <Select placeholder="请选择订单状态" allowClear>
                  <Option value="待付款">待付款</Option>
                  <Option value="待接单">待接单</Option>
                  <Option value="待服务">待服务</Option>
                  <Option value="已出发">已出发</Option>
                  <Option value="服务中">服务中</Option>
                  <Option value="已完成">已完成</Option>
                  <Option value="已评价">已评价</Option>
                  <Option value="已取消">已取消</Option>
                  <Option value="已退款">已退款</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={10}>
              <Form.Item label=" " colon={false}>
                <Space>
                  <Button type="primary" onClick={handleSearch}>
                    查询
                  </Button>
                  <Button onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      ),
    },
  ];

  return (
    <div>
      {/* 查询条件 */}
      <Collapse items={items} defaultActiveKey={['1']} />
    </div>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(DetailList);
